{"functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["pnpm --prefix \"$RESOURCE_DIR\" run lint", "pnpm --prefix \"$RESOURCE_DIR\" run build"]}], "emulators": {"functions": {"port": 5001}, "firestore": {"port": 8081}, "database": {"port": 9000}, "pubsub": {"port": 8085}, "ui": {"enabled": true}, "singleProjectMode": true}}