/**
 * Cloud Functions 测试脚本 (TypeScript 版本)
 * 用于验证 Firebase Functions 服务是否正常工作
 */

import { registerUser, createAlert, getDeviceID, getFCMToken } from '../services/firebaseFunctions';

async function testCloudFunctions(): Promise<void> {
  console.log('🚀 开始测试 Cloud Functions 集成...\n');

  try {
    // 测试获取设备 ID
    console.log('1. 测试获取设备 ID...');
    const deviceID = await getDeviceID();
    console.log(`✅ 设备 ID: ${deviceID}\n`);

    // 测试获取 FCM Token
    console.log('2. 测试获取 FCM Token...');
    const fcmToken = await getFCMToken();
    console.log(`✅ FCM Token: ${fcmToken ? fcmToken.substring(0, 50) + '...' : '空 (模拟器环境)'}\n`);

    // 测试用户注册
    console.log('3. 测试用户注册...');
    const registerResult = await registerUser({
      nickname: 'Test User',
      name: 'Test User',
      role: 'developer',
      initials: 'TU',
      color: '#3B82F6',
      phoneNumber: '+85212345678',
      avatar: '',
    });
    console.log('✅ 注册结果:', registerResult);
    console.log();

    // 测试创建通知
    console.log('4. 测试创建通知...');
    const alertResult = await createAlert({
      caseType: 'mother_baby_transfer',
      motherInitial: 'TM',
      bedNumber: '123',
      designatedWard: 'NICU',
      clinicalNotes: '测试通知',
      recipientDeviceIDs: ['staff_1', 'staff_2'], // 使用测试用户 ID
    });
    console.log('✅ 通知创建结果:', alertResult);
    console.log();

    console.log('🎉 所有测试完成！Cloud Functions 集成正常工作。');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('\n可能的解决方案:');
    console.error('1. 确保 Firebase Functions 正在运行 (firebase emulators:start)');
    console.error('2. 检查网络连接');
    console.error('3. 验证 Firebase 配置是否正确');
    console.error('4. 确保在真实设备上运行测试（某些功能需要设备支持）');
    process.exit(1);
  }
}

// 直接执行测试函数
testCloudFunctions().catch((error) => {
  console.error('测试脚本执行失败:', error);
  process.exit(1);
});

export { testCloudFunctions }; 