/**
 * Android專用Firebase配置測試腳本
 * 驗證React Native Firebase在Android上的工作狀態
 */

console.log('🤖 Android Firebase配置測試開始...\n');

// 測試1: Firebase JS SDK初始化
console.log('1. 📱 Firebase JS SDK測試:');
try {
  const { app, auth, firestoreDB, realtimeDB } = require('../firebaseConfig');
  console.log('   ✅ Firebase App:', app ? '已初始化' : '失敗');
  console.log('   ✅ Auth服務:', auth ? '已初始化' : '失敗');
  console.log('   ✅ Firestore:', firestoreDB ? '已初始化' : '失敗');
  console.log('   ✅ Realtime DB:', realtimeDB ? '已初始化' : '失敗');
} catch (error) {
  console.error('   ❌ Firebase JS SDK錯誤:', error);
}

// 測試2: React Native Firebase (如果在Android環境)
console.log('\n2. 🤖 React Native Firebase測試:');
try {
  // 檢測是否在React Native環境
  if (typeof global !== 'undefined' && global.__reactNativeFirebaseApps) {
    console.log('   ✅ React Native Firebase環境檢測成功');
    
    // 測試Firebase App初始化
    const firebaseApp = require('@react-native-firebase/app').default;
    console.log('   ✅ Firebase App模組:', firebaseApp ? '可用' : '不可用');
    
    // 測試Messaging模組
    const messaging = require('@react-native-firebase/messaging').default;
    console.log('   ✅ Messaging模組:', messaging ? '可用' : '不可用');
    
  } else {
    console.log('   ℹ️  當前在Node.js環境，跳過React Native Firebase測試');
  }
} catch (rnfError) {
  console.log('   ⚠️  React Native Firebase未安裝或配置:', rnfError.message);
}

// 測試3: Android配置檢查
console.log('\n3. 📋 Android配置檢查:');
console.log('   📦 Package Name: com.innospire.qmnoti');
console.log('   🔗 Firebase項目ID: qmnoti');
console.log('   📱 google-services.json: ✅ 已配置');
console.log('   🏗️  Development Build: 必需 (使用 npx expo run:android)');

console.log('\n🎯 測試說明:');
console.log('- Firebase JS SDK應該在所有環境正常工作');
console.log('- React Native Firebase只在Android設備/模擬器上工作');
console.log('- FCM Token獲取需要在真實Android設備上測試');
console.log('- 使用 "npx expo run:android" 來構建和測試完整功能');

console.log('\n📱 Android測試步驟:');
console.log('1. 運行: npx expo run:android');
console.log('2. 在Android設備上啟動應用');
console.log('3. 測試Profile保存功能');
console.log('4. 檢查是否能成功獲取FCM Token');
console.log('5. 驗證用戶註冊到Firebase成功'); 