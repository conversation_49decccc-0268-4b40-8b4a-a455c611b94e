import { initializeApp, getApps, type FirebaseApp } from 'firebase/app';
// const { getAnalytics } = require("firebase/analytics");
import { getAuth, type Auth } from 'firebase/auth';
import { getDatabase, type Database } from 'firebase/database';
import { getFirestore, type Firestore } from 'firebase/firestore';

const firebaseConfig = {
  apiKey: "AIzaSyDWmByCJlz3zwo5TE8Vd2Ed4EKmWUlg0Qg",
  authDomain: "qmnoti.firebaseapp.com",
  projectId: "qmnoti",
  storageBucket: "qmnoti.appspot.com",
  messagingSenderId: "590755555605",
  appId: "1:590755555605:web:660cb1745c42231729211c",
  measurementId: "G-8F79PF308H",
  databaseURL: "https://qmnoti-default-rtdb.asia-southeast1.firebasedatabase.app/"
};

// 避免重复初始化Firebase App
let app: FirebaseApp;
if (getApps().length === 0) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApps()[0];
}

// 初始化服务 - 只在需要時初始化
const firestoreDB: Firestore = getFirestore(app);
const realtimeDB: Database = getDatabase(app);
const auth: Auth = getAuth(app);

// 如果你需要 Analytics
// const analytics = getAnalytics(app);

// Android專用：確保React Native Firebase也能工作
console.log('Firebase JS SDK 初始化完成');

export { app, auth, firestoreDB, realtimeDB };
