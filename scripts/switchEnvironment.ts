/**
 * Firebase 環境切換腳本
 * 用於在開發（模擬器）和生產環境之間切換
 */

import { writeFileSync, readFileSync } from 'fs';
import { join } from 'path';

interface EnvironmentConfig {
  name: string;
  description: string;
  env: string;
  firebaseEnv: string;
}

const environments: Record<string, EnvironmentConfig> = {
  development: {
    name: '開發環境 (模擬器)',
    description: '使用本地 Firebase 模擬器，適合開發和測試',
    env: 'development',
    firebaseEnv: 'emulator'
  },
  production: {
    name: '生產環境',
    description: '使用真實的 Firebase 服務，適合正式部署',
    env: 'production',
    firebaseEnv: 'production'
  }
};

function updateEnvFile(targetEnv: string): void {
  const envPath = join(process.cwd(), '.env');
  const config = environments[targetEnv];
  
  if (!config) {
    throw new Error(`未知的環境: ${targetEnv}`);
  }

  let envContent = '';
  
  try {
    envContent = readFileSync(envPath, 'utf8');
  } catch (error) {
    console.log('📝 創建新的 .env 文件...');
  }

  // 更新或添加 FIREBASE_ENV
  const lines = envContent.split('\n');
  let firebaseEnvUpdated = false;
  let nodeEnvUpdated = false;

  const updatedLines = lines.map(line => {
    if (line.startsWith('FIREBASE_ENV=')) {
      firebaseEnvUpdated = true;
      return `FIREBASE_ENV=${config.firebaseEnv}`;
    }
    if (line.startsWith('NODE_ENV=')) {
      nodeEnvUpdated = true;
      return `NODE_ENV=${config.env}`;
    }
    return line;
  });

  if (!firebaseEnvUpdated) {
    updatedLines.push(`FIREBASE_ENV=${config.firebaseEnv}`);
  }
  if (!nodeEnvUpdated) {
    updatedLines.push(`NODE_ENV=${config.env}`);
  }

  // 添加環境說明註釋
  const finalContent = [
    `# Firebase 環境配置 - ${config.name}`,
    `# ${config.description}`,
    `# 最後更新: ${new Date().toLocaleString('zh-TW')}`,
    '',
    ...updatedLines.filter(line => line.trim() !== '')
  ].join('\n');

  writeFileSync(envPath, finalContent);
  console.log(`✅ 已更新 .env 文件為 ${config.name}`);
}

function showCurrentEnvironment(): void {
  const firebaseEnv = process.env.FIREBASE_ENV;
  const nodeEnv = process.env.NODE_ENV;
  
  console.log('📊 當前環境狀態:');
  console.log(`   FIREBASE_ENV: ${firebaseEnv || '未設置'}`);
  console.log(`   NODE_ENV: ${nodeEnv || '未設置'}`);
  
  if (firebaseEnv === 'emulator' || nodeEnv === 'development') {
    console.log('   🔧 當前配置: 開發環境 (模擬器)');
  } else if (firebaseEnv === 'production' || nodeEnv === 'production') {
    console.log('   🚀 當前配置: 生產環境');
  } else {
    console.log('   ⚠️  當前配置: 未明確設置，可能使用默認值');
  }
}

function showUsage(): void {
  console.log('🔄 Firebase 環境切換工具\n');
  console.log('用法:');
  console.log('  pnpm switch-env development  # 切換到開發環境 (模擬器)');
  console.log('  pnpm switch-env production   # 切換到生產環境');
  console.log('  pnpm switch-env status       # 查看當前環境狀態');
  console.log('  pnpm switch-env help         # 顯示此幫助信息\n');
  
  console.log('可用環境:');
  Object.entries(environments).forEach(([key, config]) => {
    console.log(`  ${key.padEnd(12)} - ${config.name}`);
    console.log(`  ${' '.repeat(15)} ${config.description}`);
  });
}

async function main(): Promise<void> {
  const targetEnv = process.argv[2];

  if (!targetEnv || targetEnv === 'help') {
    showUsage();
    return;
  }

  if (targetEnv === 'status') {
    showCurrentEnvironment();
    return;
  }

  if (!environments[targetEnv]) {
    console.error(`❌ 錯誤: 未知的環境 "${targetEnv}"`);
    console.log('\n可用環境: ' + Object.keys(environments).join(', '));
    process.exit(1);
  }

  try {
    console.log(`🔄 切換到 ${environments[targetEnv].name}...`);
    updateEnvFile(targetEnv);
    
    console.log('\n📋 下一步操作:');
    if (targetEnv === 'development') {
      console.log('1. 啟動 Firebase 模擬器: firebase emulators:start');
      console.log('2. 重新啟動 React Native 應用');
      console.log('3. 測試功能: pnpm firebase-connection-test');
    } else if (targetEnv === 'production') {
      console.log('1. 確保 Firebase 項目已升級到 Blaze 計劃');
      console.log('2. 部署 Cloud Functions: firebase deploy --only functions');
      console.log('3. 重新啟動 React Native 應用');
      console.log('4. 測試功能: pnpm production-test');
    }
    
    console.log('\n⚠️  注意: 請重新啟動應用以使環境變更生效');
    
  } catch (error) {
    console.error('❌ 環境切換失敗:', error);
    process.exit(1);
  }
}

main();
