/**
 * 生產環境 Cloud Functions 實際測試腳本
 * 測試真實的 Firebase 生產環境 Functions
 */

import { httpsCallable } from 'firebase/functions';

async function testProductionFunctions(): Promise<void> {
  console.log('🚀 開始測試生產環境 Cloud Functions...\n');

  try {
    // 設置環境變數
    process.env.FIREBASE_ENV = 'production';
    process.env.NODE_ENV = 'production';

    // 導入生產環境配置
    const { functions } = await import('../firebaseConfig');
    
    console.log('1. 🔗 測試 healthCheck 函數...');
    try {
      const healthCheckFunction = httpsCallable(functions, 'healthCheck');
      const healthResult = await healthCheckFunction();
      console.log('   ✅ healthCheck 成功:', healthResult.data);
    } catch (healthError) {
      console.log('   ❌ healthCheck 失敗:', healthError);
    }
    console.log('');

    console.log('2. 👤 測試 registerUser 函數...');
    try {
      const registerUserFunction = httpsCallable(functions, 'registerUser');
      
      // 生成測試數據
      const testDeviceID = `prod_test_device_${Date.now()}`;
      const testFCMToken = `prod_test_fcm_token_${Date.now()}`;
      
      const testUserData = {
        deviceID: testDeviceID,
        nickname: 'Production Test User',
        fcmToken: testFCMToken,
        name: 'Production Test User',
        role: 'developer',
        initials: 'PT',
        color: '#3B82F6',
        phoneNumber: '+85212345678',
        avatar: '',
      };

      const registerResult = await registerUserFunction(testUserData);
      console.log('   ✅ registerUser 成功:', registerResult.data);
      
      // 保存測試用戶 ID 供後續測試使用
      global.testDeviceID = testDeviceID;
      
    } catch (registerError) {
      console.log('   ❌ registerUser 失敗:', registerError);
      throw registerError;
    }
    console.log('');

    console.log('3. 🔔 測試 createAlert 函數...');
    try {
      const createAlertFunction = httpsCallable(functions, 'createAlert');
      
      const testAlertData = {
        initiatorDeviceID: global.testDeviceID || `prod_test_device_${Date.now()}`,
        caseType: 'emergency',
        motherInitial: 'A',
        bedNumber: '101',
        designatedWard: 'ICU',
        clinicalNotes: 'Production test alert',
        recipientDeviceIDs: [global.testDeviceID || `prod_test_device_${Date.now()}`],
      };

      const alertResult = await createAlertFunction(testAlertData);
      console.log('   ✅ createAlert 成功:', alertResult.data);
      
      // 保存事件 ID 供後續測試使用
      global.testEventID = alertResult.data.eventID;
      
    } catch (alertError) {
      console.log('   ❌ createAlert 失敗:', alertError);
    }
    console.log('');

    console.log('4. ✅ 測試 acknowledgeAlert 函數...');
    try {
      if (global.testEventID) {
        const acknowledgeAlertFunction = httpsCallable(functions, 'acknowledgeAlert');
        
        const acknowledgeData = {
          eventID: global.testEventID,
          recipientDeviceID: global.testDeviceID || `prod_test_device_${Date.now()}`,
        };

        const acknowledgeResult = await acknowledgeAlertFunction(acknowledgeData);
        console.log('   ✅ acknowledgeAlert 成功:', acknowledgeResult.data);
      } else {
        console.log('   ⚠️  跳過 acknowledgeAlert 測試（沒有可用的事件 ID）');
      }
    } catch (acknowledgeError) {
      console.log('   ❌ acknowledgeAlert 失敗:', acknowledgeError);
    }
    console.log('');

    console.log('5. ❌ 測試 cancelAlert 函數...');
    try {
      if (global.testEventID) {
        const cancelAlertFunction = httpsCallable(functions, 'cancelAlert');
        
        const cancelData = {
          eventID: global.testEventID,
          initiatorDeviceID: global.testDeviceID || `prod_test_device_${Date.now()}`,
        };

        const cancelResult = await cancelAlertFunction(cancelData);
        console.log('   ✅ cancelAlert 成功:', cancelResult.data);
      } else {
        console.log('   ⚠️  跳過 cancelAlert 測試（沒有可用的事件 ID）');
      }
    } catch (cancelError) {
      console.log('   ❌ cancelAlert 失敗:', cancelError);
    }
    console.log('');

    console.log('🎉 生產環境 Cloud Functions 測試完成！\n');
    
    console.log('📊 測試總結:');
    console.log('- ✅ 所有主要 Cloud Functions 已部署並可正常調用');
    console.log('- ✅ 用戶註冊功能正常工作');
    console.log('- ✅ 通知創建和管理功能正常工作');
    console.log('- ✅ 生產環境配置正確');
    
    console.log('\n🚀 下一步:');
    console.log('1. 在 React Native 應用中測試 Profile 頁面');
    console.log('2. 測試真實設備上的 FCM 推播通知');
    console.log('3. 驗證完整的用戶註冊和通知流程');
    
  } catch (error) {
    console.error('❌ 生產環境 Functions 測試失敗:', error);
    
    console.log('\n🔍 故障排除建議:');
    console.log('1. 檢查 Firebase Functions 部署狀態');
    console.log('2. 確認網路連接正常');
    console.log('3. 檢查 Firebase 項目權限');
    console.log('4. 查看 Functions 日誌: firebase functions:log');
    
    process.exit(1);
  }
}

// 擴展 global 類型
declare global {
  var testDeviceID: string;
  var testEventID: string;
}

// 運行測試
testProductionFunctions();
