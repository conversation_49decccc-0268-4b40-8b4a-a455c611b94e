/**
 * Firebase Functions 服务
 * 用於調用 Cloud Functions 中定義的 registerUser 和 createAlert 函數
 */

import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebaseConfig';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import Constants from 'expo-constants';

// Functions 實例已從 firebaseConfig 導入

// 類型定義，對應 Cloud Functions 中的類型
interface RegisterUserRequest {
  deviceID: string;
  nickname: string;
  fcmToken: string;
  name?: string;
  role?: string;
  initials?: string;
  color?: string;
  phoneNumber?: string;
  avatar?: string;
}

interface RegisterUserResponse {
  success: boolean;
  message: string;
  user?: any;
}

interface CreateAlertRequest {
  initiatorDeviceID: string;
  caseType: string;
  motherInitial: string;
  bedNumber?: string;
  designatedWard: string;
  clinicalNotes?: string;
  recipientDeviceIDs: string[];
}

interface CreateAlertResponse {
  success: boolean;
  message: string;
  eventID?: string;
  failedRecipients?: { deviceID: string; reason: string }[];
}

// 工具函數：獲取設備唯一 ID
export const getDeviceID = async (): Promise<string> => {
  try {
    // 優先使用設備的唯一識別符
    if (Device.deviceName && Device.osName && Device.osVersion) {
      return `${Device.osName}_${Device.deviceName}_${Device.osVersion}`.replace(/[^a-zA-Z0-9_-]/g, '_');
    }
    
    // 如果無法獲取設備信息，使用當前時間戳和隨機數生成
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `device_${timestamp}_${random}`;
  } catch (error) {
    console.error('生成設備 ID 時發生錯誤:', error);
    // 備用方案：使用時間戳和隨機數
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `device_${timestamp}_${random}`;
  }
};

// 工具函數：獲取 FCM Token
export const getFCMToken = async (): Promise<string> => {
  try {
    console.log('開始獲取 FCM Token...');

    // 檢查設備是否支持推播通知
    if (!Device.isDevice) {
      console.warn('推播通知需要在真實設備上運行，返回模擬器測試 Token');
      // 在模擬器環境中返回測試 token，而不是空字符串
      return 'simulator_test_token_' + Date.now();
    }

    console.log('設備檢測通過，設備類型:', Device.osName);

    // 設置Android通知頻道
    if (Device.osName === 'Android') {
      console.log('配置 Android 通知頻道...');
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
      console.log('Android 通知頻道配置完成');
    }

    // 獲取推播通知權限
    console.log('檢查推播通知權限...');
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    console.log('當前權限狀態:', existingStatus);
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      console.log('請求推播通知權限...');
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
      console.log('權限請求結果:', status);
    }

    if (finalStatus !== 'granted') {
      console.warn('用戶拒絕了推播通知權限，返回無權限測試 Token');
      // 即使權限被拒絕，也返回一個標識 token，而不是空字符串
      return 'no_permission_token_' + Date.now();
    }

    console.log('推播通知權限已獲得，嘗試獲取 FCM Token...');

    // 嘗試使用React Native Firebase獲取FCM Token (如果可用)
    try {
      console.log('嘗試使用 React Native Firebase...');
      // 使用新的模塊化 API
      const messaging = await import('@react-native-firebase/messaging');
      const messagingInstance = messaging.default();
      await messagingInstance.requestPermission();
      const fcmToken = await messagingInstance.getToken();
      console.log('使用 React Native Firebase 獲取 FCM Token 成功，Token 長度:', fcmToken?.length || 0);
      return fcmToken;
    } catch (rnfError) {
      console.log('React Native Firebase 不可用，嘗試使用 Expo Notifications...', rnfError instanceof Error ? rnfError.message : String(rnfError));

      // 回退到Expo Push Token
      const projectId = Constants?.expoConfig?.extra?.eas?.projectId ?? Constants?.easConfig?.projectId ?? 'qmnoti';

      if (!projectId) {
        console.error('未找到項目 ID，使用默認值');
        // 使用默認項目 ID 而不是拋出錯誤
        const defaultProjectId = 'qmnoti';
        console.log('使用默認項目 ID:', defaultProjectId);

        try {
          const tokenData = await Notifications.getExpoPushTokenAsync({
            projectId: defaultProjectId,
          });
          console.log('使用默認項目 ID 獲取 Expo Push Token 成功');
          return tokenData.data;
        } catch (defaultError) {
          console.error('使用默認項目 ID 獲取 Token 失敗:', defaultError);
          // 返回錯誤標識 token 而不是拋出錯誤
          return 'expo_error_token_' + Date.now();
        }
      }

      console.log('使用項目 ID:', projectId);

      try {
        // 獲取 Expo Push Token
        const tokenData = await Notifications.getExpoPushTokenAsync({
          projectId,
        });

        console.log('Expo Push Token 獲取成功，Token 長度:', tokenData.data?.length || 0);
        return tokenData.data;
      } catch (expoError) {
        console.error('Expo Push Token 獲取失敗:', expoError);
        // 返回錯誤標識 token 而不是拋出錯誤
        return 'expo_error_token_' + Date.now();
      }
    }
  } catch (error) {
    console.error('獲取 FCM Token 時發生未預期錯誤:', error);
    // 返回錯誤標識 token 而不是拋出錯誤，確保用戶註冊流程能繼續
    return 'error_token_' + Date.now();
  }
};

/**
 * 註冊用戶 Cloud Function
 */
export const registerUser = async (userData: Omit<RegisterUserRequest, 'deviceID' | 'fcmToken'>): Promise<RegisterUserResponse> => {
  try {
    // 獲取設備 ID 和 FCM Token
    const deviceID = await getDeviceID();
    const fcmToken = await getFCMToken();

    const registerUserFunction = httpsCallable<RegisterUserRequest, RegisterUserResponse>(functions, 'registerUser');
    
    const result = await registerUserFunction({
      deviceID,
      fcmToken,
      ...userData,
    });

    return result.data;
  } catch (error) {
    console.error('註冊用戶時發生錯誤:', error);
    throw new Error(`註冊失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
};

/**
 * 創建通知事件 Cloud Function
 */
export const createAlert = async (alertData: Omit<CreateAlertRequest, 'initiatorDeviceID'>): Promise<CreateAlertResponse> => {
  try {
    // 獲取發起者的設備 ID
    const initiatorDeviceID = await getDeviceID();

    const createAlertFunction = httpsCallable<CreateAlertRequest, CreateAlertResponse>(functions, 'createAlert');
    
    const result = await createAlertFunction({
      initiatorDeviceID,
      ...alertData,
    });

    return result.data;
  } catch (error) {
    console.error('創建通知事件時發生錯誤:', error);
    throw new Error(`發送通知失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
};

/**
 * 確認通知 Cloud Function
 */
export const acknowledgeAlert = async (eventID: string): Promise<any> => {
  try {
    const recipientDeviceID = await getDeviceID();

    const acknowledgeAlertFunction = httpsCallable(functions, 'acknowledgeAlert');
    
    const result = await acknowledgeAlertFunction({
      eventID,
      recipientDeviceID,
    });

    return result.data;
  } catch (error) {
    console.error('確認通知時發生錯誤:', error);
    throw new Error(`確認通知失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
};

/**
 * 取消通知 Cloud Function
 */
export const cancelAlert = async (eventID: string): Promise<any> => {
  try {
    const initiatorDeviceID = await getDeviceID();

    const cancelAlertFunction = httpsCallable(functions, 'cancelAlert');
    
    const result = await cancelAlertFunction({
      eventID,
      initiatorDeviceID,
    });

    return result.data;
  } catch (error) {
    console.error('取消通知時發生錯誤:', error);
    throw new Error(`取消通知失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
}; 