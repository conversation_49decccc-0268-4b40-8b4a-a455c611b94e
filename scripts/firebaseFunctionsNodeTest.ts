/**
 * Firebase Functions Node.js 测试服务
 * 专门用于在 Node.js 环境中测试 Cloud Functions
 */

import { initializeApp } from 'firebase/app';
import { getFunctions, httpsCallable, connectFunctionsEmulator } from 'firebase/functions';

// Firebase 配置
const firebaseConfig = {
  apiKey: "AIzaSyDWmByCJlz3zwo5TE8Vd2Ed4EKmWUlg0Qg",
  authDomain: "qmnoti.firebaseapp.com",
  projectId: "qmnoti",
  storageBucket: "qmnoti.appspot.com",
  messagingSenderId: "590755555605",
  appId: "1:590755555605:web:660cb1745c42231729211c",
  measurementId: "G-8F79PF308H",
  databaseURL: "https://qmnoti-default-rtdb.asia-southeast1.firebasedatabase.app/"
};

// 初始化 Firebase
const app = initializeApp(firebaseConfig);
const functions = getFunctions(app, 'asia-east1');

// 连接到本地 Functions 模拟器（用于测试）
console.log('🔧 连接到 Firebase Functions 模拟器 (localhost:5001)...');
connectFunctionsEmulator(functions, 'localhost', 5001);

// 类型定义
interface RegisterUserRequest {
  deviceID: string;
  nickname: string;
  fcmToken: string;
  name?: string;
  role?: string;
  initials?: string;
  color?: string;
  phoneNumber?: string;
  avatar?: string;
}

interface CreateAlertRequest {
  initiatorDeviceID: string;
  caseType: string;
  motherInitial: string;
  bedNumber?: string;
  designatedWard: string;
  clinicalNotes?: string;
  recipientDeviceIDs: string[];
}

// 生成测试设备 ID
let testDeviceID: string;

function generateTestDeviceID(): string {
  if (!testDeviceID) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    testDeviceID = `test_device_${timestamp}_${random}`;
  }
  return testDeviceID;
}

// 生成测试 FCM Token
function generateTestFCMToken(): string {
  return 'test_fcm_token_for_nodejs_environment';
}

// 测试用户注册
async function testRegisterUser(): Promise<any> {
  const registerUserFunction = httpsCallable<RegisterUserRequest, any>(functions, 'registerUser');
  
  const result = await registerUserFunction({
    deviceID: generateTestDeviceID(),
    fcmToken: generateTestFCMToken(),
    nickname: 'Test User',
    name: 'Test User',
    role: 'developer',
    initials: 'TU',
    color: '#3B82F6',
    phoneNumber: '+85212345678',
    avatar: '',
  });

  return result.data;
}

// 测试创建通知
async function testCreateAlert(): Promise<any> {
  const createAlertFunction = httpsCallable<CreateAlertRequest, any>(functions, 'createAlert');
  
  const result = await createAlertFunction({
    initiatorDeviceID: generateTestDeviceID(),
    caseType: 'mother_baby_transfer',
    motherInitial: 'TM',
    bedNumber: '123',
    designatedWard: 'NICU',
    clinicalNotes: '测试通知',
    recipientDeviceIDs: [generateTestDeviceID()],
  });

  return result.data;
}

// 测试确认通知
async function testAcknowledgeAlert(eventID: string): Promise<any> {
  const acknowledgeAlertFunction = httpsCallable(functions, 'acknowledgeAlert');
  
  const result = await acknowledgeAlertFunction({
    eventID,
    recipientDeviceID: generateTestDeviceID(),
  });

  return result.data;
}

async function runTests(): Promise<void> {
  console.log('🚀 开始测试 Cloud Functions 集成 (Node.js 环境)...\n');

  try {
    // 测试设备 ID 生成
    console.log('1. 测试设备 ID 生成...');
    const deviceID = generateTestDeviceID();
    console.log(`✅ 设备 ID: ${deviceID}\n`);

    // 测试 FCM Token 生成
    console.log('2. 测试 FCM Token 生成...');
    const fcmToken = generateTestFCMToken();
    console.log(`✅ FCM Token: ${fcmToken}\n`);

    // 测试用户注册 Cloud Function
    console.log('3. 测试用户注册 Cloud Function...');
    const registerResult = await testRegisterUser();
    console.log('✅ 注册结果:', registerResult);
    console.log();

    // 等待一秒确保数据写入完成
    console.log('⏳ 等待数据写入完成...');
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 测试创建通知 Cloud Function
    console.log('4. 测试创建通知 Cloud Function...');
    const alertResult = await testCreateAlert();
    console.log('✅ 通知创建结果:', alertResult);
    console.log();

    // 如果创建通知成功，测试确认通知
    if (alertResult && alertResult.success && alertResult.eventID) {
      console.log('5. 测试确认通知 Cloud Function...');
      const ackResult = await testAcknowledgeAlert(alertResult.eventID);
      console.log('✅ 确认通知结果:', ackResult);
      console.log();
    }

    console.log('🎉 所有测试完成！Cloud Functions 在 Node.js 环境中正常工作。');

  } catch (error: any) {
    console.error('❌ 测试失败:', error);
    
    if (error.code) {
      console.error(`错误代码: ${error.code}`);
    }
    if (error.message) {
      console.error(`错误信息: ${error.message}`);
    }
    
    console.error('\n可能的解决方案:');
    console.error('1. 确保 Firebase Functions 正在运行:');
    console.error('   cd functions && npm run serve');
    console.error('   或者 firebase emulators:start');
    console.error('2. 检查网络连接到 Firebase 项目');
    console.error('3. 验证 Firebase 配置是否正确');
    console.error('4. 检查 Cloud Functions 是否已部署');
    
    process.exit(1);
  }
}

// 执行测试
runTests();

export { testRegisterUser, testCreateAlert, runTests }; 