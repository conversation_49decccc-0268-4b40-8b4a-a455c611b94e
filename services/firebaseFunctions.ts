/**
 * Firebase Functions 服务
 * 用於調用 Cloud Functions 中定義的 registerUser 和 createAlert 函數
 */

import { getFunctions, httpsCallable } from 'firebase/functions';
import { app } from '../firebaseConfig';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import Constants from 'expo-constants';

// 獲取 Functions 實例
const functions = getFunctions(app, 'asia-east1'); // 使用與 Cloud Functions 相同的 region

// 類型定義，對應 Cloud Functions 中的類型
interface RegisterUserRequest {
  deviceID: string;
  nickname: string;
  fcmToken: string;
  name?: string;
  role?: string;
  initials?: string;
  color?: string;
  phoneNumber?: string;
  avatar?: string;
}

interface RegisterUserResponse {
  success: boolean;
  message: string;
  user?: any;
}

interface CreateAlertRequest {
  initiatorDeviceID: string;
  caseType: string;
  motherInitial: string;
  bedNumber?: string;
  designatedWard: string;
  clinicalNotes?: string;
  recipientDeviceIDs: string[];
}

interface CreateAlertResponse {
  success: boolean;
  message: string;
  eventID?: string;
  failedRecipients?: { deviceID: string; reason: string }[];
}

// 工具函數：獲取設備唯一 ID
export const getDeviceID = async (): Promise<string> => {
  try {
    // 優先使用設備的唯一識別符
    if (Device.deviceName && Device.osName && Device.osVersion) {
      return `${Device.osName}_${Device.deviceName}_${Device.osVersion}`.replace(/[^a-zA-Z0-9_-]/g, '_');
    }
    
    // 如果無法獲取設備信息，使用當前時間戳和隨機數生成
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `device_${timestamp}_${random}`;
  } catch (error) {
    console.error('生成設備 ID 時發生錯誤:', error);
    // 備用方案：使用時間戳和隨機數
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `device_${timestamp}_${random}`;
  }
};

// 工具函數：獲取 FCM Token
export const getFCMToken = async (): Promise<string> => {
  try {
    // 檢查設備是否支持推播通知
    if (!Device.isDevice) {
      console.warn('推播通知需要在真實設備上運行');
      return '';
    }

    // 設置Android通知頻道
    if (Device.osName === 'Android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }

    // 獲取推播通知權限
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    
    if (finalStatus !== 'granted') {
      console.warn('用戶拒絕了推播通知權限');
      return '';
    }

    // 嘗試使用React Native Firebase獲取FCM Token (如果可用)
    try {
      const messaging = require('@react-native-firebase/messaging').default;
      await messaging().requestPermission();
      const fcmToken = await messaging().getToken();
      console.log('使用React Native Firebase獲取FCM Token成功');
      return fcmToken;
    } catch (rnfError) {
      console.log('React Native Firebase不可用，嘗試使用Expo Notifications...');
      
      // 回退到Expo Push Token
      const projectId = Constants?.expoConfig?.extra?.eas?.projectId ?? Constants?.easConfig?.projectId ?? 'qmnoti';
      
      if (!projectId) {
        throw new Error('Project ID not found in app configuration');
      }

      console.log('使用項目ID:', projectId);

      // 獲取 Expo Push Token
      const tokenData = await Notifications.getExpoPushTokenAsync({
        projectId,
      });
      
      console.log('Expo Push Token 獲取成功');
      return tokenData.data;
    }
  } catch (error) {
    console.error('獲取 FCM Token 時發生錯誤:', error);
    throw error; // 重新拋出錯誤以便上層處理
  }
};

/**
 * 註冊用戶 Cloud Function
 */
export const registerUser = async (userData: Omit<RegisterUserRequest, 'deviceID' | 'fcmToken'>): Promise<RegisterUserResponse> => {
  try {
    // 獲取設備 ID 和 FCM Token
    const deviceID = await getDeviceID();
    const fcmToken = await getFCMToken();

    const registerUserFunction = httpsCallable<RegisterUserRequest, RegisterUserResponse>(functions, 'registerUser');
    
    const result = await registerUserFunction({
      deviceID,
      fcmToken,
      ...userData,
    });

    return result.data;
  } catch (error) {
    console.error('註冊用戶時發生錯誤:', error);
    throw new Error(`註冊失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
};

/**
 * 創建通知事件 Cloud Function
 */
export const createAlert = async (alertData: Omit<CreateAlertRequest, 'initiatorDeviceID'>): Promise<CreateAlertResponse> => {
  try {
    // 獲取發起者的設備 ID
    const initiatorDeviceID = await getDeviceID();

    const createAlertFunction = httpsCallable<CreateAlertRequest, CreateAlertResponse>(functions, 'createAlert');
    
    const result = await createAlertFunction({
      initiatorDeviceID,
      ...alertData,
    });

    return result.data;
  } catch (error) {
    console.error('創建通知事件時發生錯誤:', error);
    throw new Error(`發送通知失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
};

/**
 * 確認通知 Cloud Function
 */
export const acknowledgeAlert = async (eventID: string): Promise<any> => {
  try {
    const recipientDeviceID = await getDeviceID();

    const acknowledgeAlertFunction = httpsCallable(functions, 'acknowledgeAlert');
    
    const result = await acknowledgeAlertFunction({
      eventID,
      recipientDeviceID,
    });

    return result.data;
  } catch (error) {
    console.error('確認通知時發生錯誤:', error);
    throw new Error(`確認通知失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
};

/**
 * 取消通知 Cloud Function
 */
export const cancelAlert = async (eventID: string): Promise<any> => {
  try {
    const initiatorDeviceID = await getDeviceID();

    const cancelAlertFunction = httpsCallable(functions, 'cancelAlert');
    
    const result = await cancelAlertFunction({
      eventID,
      initiatorDeviceID,
    });

    return result.data;
  } catch (error) {
    console.error('取消通知時發生錯誤:', error);
    throw new Error(`取消通知失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
}; 