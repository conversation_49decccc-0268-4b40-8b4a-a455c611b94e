/**
 * Firebase 客戶端服務
 * 提供 Firestore、Realtime Database 和 Cloud Functions 的統一接口
 */

import { initializeApp, getApps } from 'firebase/app';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getDatabase, connectDatabaseEmulator } from 'firebase/database';
import { getFunctions, connectFunctionsEmulator, httpsCallable } from 'firebase/functions';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
// 暂时使用空配置，实际使用时需要配置真实的 Firebase 项目
const firebaseConfig = {
  apiKey: "",
  authDomain: "",
  databaseURL: "",
  projectId: "",
  storageBucket: "",
  messagingSenderId: "",
  appId: ""
};

// Firebase 類型導入
import type {
  RegisterUserRequest,
  RegisterUserResponse,
  CreateAlertRequest,
  CreateAlertResponse,
  AcknowledgeAlertRequest,
  AcknowledgeAlertResponse,
  CancelAlertRequest,
  CancelAlertResponse,
} from '@/types/firebase';

/**
 * 初始化 Firebase App (避免重複初始化)
 */
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];

/**
 * 獲取 Firebase 服務實例
 */
export const firestore = getFirestore(app);
export const realtimeDb = getDatabase(app);
export const functions = getFunctions(app);
export const auth = getAuth(app);

/**
 * 開發環境模擬器配置
 */
// 開發環境模擬器配置 (暂时注释，需要时启用)
// if (__DEV__) {
//   try {
//     connectFirestoreEmulator(firestore, 'localhost', 8080);
//     connectDatabaseEmulator(realtimeDb, 'localhost', 9000);
//     connectFunctionsEmulator(functions, 'localhost', 5001);
//     connectAuthEmulator(auth, 'http://localhost:9099');
//   } catch (error) {
//     console.log('Firebase 模擬器連接設置:', error);
//   }
// }

/**
 * Cloud Functions 客戶端接口
 */

/**
 * 用戶註冊/更新 Cloud Function
 */
export const registerUser = httpsCallable<RegisterUserRequest, RegisterUserResponse>(
  functions,
  'registerUser'
);

/**
 * 創建通知事件 Cloud Function
 */
export const createAlert = httpsCallable<CreateAlertRequest, CreateAlertResponse>(
  functions,
  'createAlert'
);

/**
 * 確認通知 Cloud Function
 */
export const acknowledgeAlert = httpsCallable<AcknowledgeAlertRequest, AcknowledgeAlertResponse>(
  functions,
  'acknowledgeAlert'
);

/**
 * 取消通知 Cloud Function
 */
export const cancelAlert = httpsCallable<CancelAlertRequest, CancelAlertResponse>(
  functions,
  'cancelAlert'
);

/**
 * 初始化員工數據 Cloud Function
 */
export const initializeStaffData = httpsCallable(functions, 'initializeStaffData');

/**
 * 健康檢查 Cloud Function
 */
export const healthCheck = httpsCallable(functions, 'healthCheck');

/**
 * Firebase 服務包裝器類
 * 提供統一的錯誤處理和類型安全的接口
 */
export class FirebaseService {
  /**
   * 用戶註冊
   */
  static async registerUser(userData: RegisterUserRequest): Promise<RegisterUserResponse> {
    try {
      const result = await registerUser(userData);
      return result.data;
    } catch (error: any) {
      console.error('用戶註冊錯誤:', error);
      throw new Error(error.message || '用戶註冊失敗');
    }
  }

  /**
   * 創建通知事件
   */
  static async createAlert(alertData: CreateAlertRequest): Promise<CreateAlertResponse> {
    try {
      const result = await createAlert(alertData);
      return result.data;
    } catch (error: any) {
      console.error('創建通知事件錯誤:', error);
      throw new Error(error.message || '創建通知事件失敗');
    }
  }

  /**
   * 確認通知
   */
  static async acknowledgeAlert(acknowledgeData: AcknowledgeAlertRequest): Promise<AcknowledgeAlertResponse> {
    try {
      const result = await acknowledgeAlert(acknowledgeData);
      return result.data;
    } catch (error: any) {
      console.error('確認通知錯誤:', error);
      throw new Error(error.message || '確認通知失敗');
    }
  }

  /**
   * 取消通知
   */
  static async cancelAlert(cancelData: CancelAlertRequest): Promise<CancelAlertResponse> {
    try {
      const result = await cancelAlert(cancelData);
      return result.data;
    } catch (error: any) {
      console.error('取消通知錯誤:', error);
      throw new Error(error.message || '取消通知失敗');
    }
  }

  /**
   * 初始化員工數據
   */
  static async initializeStaffData(): Promise<any> {
    try {
      const result = await initializeStaffData();
      return result.data;
    } catch (error: any) {
      console.error('初始化員工數據錯誤:', error);
      throw new Error(error.message || '初始化員工數據失敗');
    }
  }

  /**
   * 健康檢查
   */
  static async healthCheck(): Promise<any> {
    try {
      const result = await healthCheck();
      return result.data;
    } catch (error: any) {
      console.error('健康檢查錯誤:', error);
      throw new Error(error.message || '健康檢查失敗');
    }
  }
}

/**
 * Firebase 連接狀態檢查
 */
export const checkFirebaseConnection = async (): Promise<boolean> => {
  try {
    await FirebaseService.healthCheck();
    return true;
  } catch (error) {
    console.error('Firebase 連接檢查失敗:', error);
    return false;
  }
};

/**
 * 導出所有服務實例以便其他模組使用
 */
export default {
  app,
  firestore,
  realtimeDb,
  functions,
  auth,
  FirebaseService,
  checkFirebaseConnection,
}; 