/**
 * Firebase 連接測試腳本
 * 用於驗證 Firebase 模擬器連接和 API 修復
 */

import { registerUser, getDeviceID, getFCMToken } from '../services/firebaseFunctions';

async function testFirebaseConnection(): Promise<void> {
  console.log('🔧 開始測試 Firebase 連接修復...\n');

  try {
    // 1. 測試設備 ID 獲取
    console.log('1. 📱 測試設備 ID 獲取...');
    const deviceID = await getDeviceID();
    console.log(`   ✅ 設備 ID: ${deviceID}\n`);

    // 2. 測試 FCM Token 獲取
    console.log('2. 🔔 測試 FCM Token 獲取...');
    const fcmToken = await getFCMToken();
    console.log(`   ✅ FCM Token: ${fcmToken.substring(0, 50)}...`);
    console.log(`   ℹ️  Token 長度: ${fcmToken.length}\n`);

    // 3. 測試用戶註冊到 Cloud Functions
    console.log('3. 👤 測試用戶註冊到 Cloud Functions...');
    try {
      const testUserData = {
        nickname: 'Test User Fix',
        name: 'Test User Fix',
        role: 'developer' as const,
        initials: 'TF',
        color: '#3B82F6',
        phoneNumber: '+85212345678',
        avatar: '',
      };

      const result = await registerUser(testUserData);
      console.log('   ✅ 用戶註冊成功!');
      console.log(`   📝 結果: ${JSON.stringify(result, null, 2)}\n`);

    } catch (registrationError) {
      console.log(`   ❌ 用戶註冊失敗: ${registrationError instanceof Error ? registrationError.message : String(registrationError)}\n`);
      throw registrationError;
    }

    console.log('🎉 所有測試通過！Firebase 連接修復成功！\n');
    
    console.log('📊 修復總結:');
    console.log('- ✅ Firebase 模擬器連接配置已添加');
    console.log('- ✅ React Native Firebase API 已更新到新的模塊化 API');
    console.log('- ✅ "not-found" 錯誤已解決');
    console.log('- ✅ 用戶註冊流程正常工作');
    console.log('- ✅ FCM Token 獲取正常');
    
  } catch (error) {
    console.error('❌ 測試失敗:', error);
    
    console.log('\n🔍 故障排除建議:');
    console.log('1. 確保 Firebase 模擬器正在運行: firebase emulators:start');
    console.log('2. 檢查模擬器端口配置是否正確');
    console.log('3. 確認 React Native Firebase 依賴已正確安裝');
    console.log('4. 檢查網路連接');
    
    process.exit(1);
  }
}

// 運行測試
if (require.main === module) {
  testFirebaseConnection().catch(console.error);
}

export { testFirebaseConnection };
