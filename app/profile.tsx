/**
 * User Profile Edit Page
 * Implementation based on Profile.html design file
 */

import { MaterialIcons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Alert,
  Animated,
  ScrollView,
  StyleSheet,
  Text as RNText,
  TouchableOpacity,
  View
} from 'react-native';
import {
  Appbar,
  Avatar,
  Button,
  Card,
  IconButton,
  Menu,
  Portal,
  Snackbar,
  TextInput,
  useTheme
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

import { StorageService } from '@/services/storage';
import { registerUser } from '@/services/firebaseFunctions';
import { PROFILE_COLORS, ROLE_OPTIONS, UserProfile, UserRole } from '@/types/profile';

// Utility function: Check if color is light
const isLightColor = (hexColor: string): boolean => {
  const hex = hexColor.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  return brightness > 155;
};

// Utility function: Format phone number
const formatPhoneNumber = (phone: string): string => {
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  // Remove 852 prefix if present
  if (digits.startsWith('852')) {
    return digits.substring(3);
  }
  
  return digits;
};

// Utility function: Generate initials
const generateInitials = (name: string): string => {
  if (!name.trim()) return '';
  
  const words = name.trim().split(/\s+/);
  if (words.length === 1) {
    // Single word, take first two characters
    return words[0].substring(0, 2).toUpperCase();
  } else {
    // Multiple words, take first letter of each word
    return words.map(word => word.charAt(0).toUpperCase()).join('').substring(0, 3);
  }
};

export default function ProfileScreen() {
  const theme = useTheme();
  
  // Form state
  const [name, setName] = useState('');
  const [initials, setInitials] = useState('');
  const [role, setRole] = useState<UserRole>('developer');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [selectedColor, setSelectedColor] = useState<string>(PROFILE_COLORS[1]); // Default blue
  
  // UI state
  const [loading, setLoading] = useState(false);
  const [showRoleMenu, setShowRoleMenu] = useState(false);
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // 動畫狀態 - 為頭像顏色變化添加動畫
  const avatarColorAnimation = useMemo(() => new Animated.Value(1), []);
  const [colorAnimations] = useState(() =>
    PROFILE_COLORS.reduce((acc, color) => {
      acc[color] = new Animated.Value(1);
      return acc;
    }, {} as Record<string, Animated.Value>)
  );

  // Dynamic styles with full dark mode support
  const dynamicStyles = useMemo(() => ({
    container: { backgroundColor: theme.colors.background },
    header: { backgroundColor: theme.colors.primary },
    surface: { backgroundColor: theme.colors.surface },
    cardBackground: { backgroundColor: theme.colors.surfaceVariant },
    textColor: { color: theme.colors.onSurface },
    placeholderColor: theme.colors.onSurfaceVariant,
    contentBackground: { backgroundColor: theme.colors.background }, // Fix for dark mode
    menuButtonBackground: { backgroundColor: theme.colors.surface },
  }), [theme]);

  // Load user profile
  useEffect(() => {
    const loadUserProfile = async () => {
      try {
        const profile = await StorageService.getUserProfile();
        if (profile) {
          setName(profile.name);
          setInitials(profile.initials);
          setRole(profile.role);
          setPhoneNumber(profile.phoneNumber || '');
          setSelectedColor(profile.color);
        }
      } catch (error) {
        console.error('Failed to load user profile:', error);
      } finally {
        setIsInitialLoad(false);
      }
    };

    loadUserProfile();
  }, []);

  // Auto-generate initials
  useEffect(() => {
    if (name && !initials) {
      const autoInitials = generateInitials(name);
      setInitials(autoInitials);
    }
  }, [name, initials]);

  // 處理返回導航 - 添加觸覺反饋
  const handleGoBack = useCallback(() => {
    // 添加觸覺反饋
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    if (router.canGoBack()) {
      router.back();
    } else {
      // 如果沒有可返回的頁面，導航到主頁
      router.replace('/');
    }
  }, []);

  // 處理保存 - 添加觸覺反饋、驗證和 Cloud Functions 整合
  const handleSave = useCallback(async () => {
    // 添加觸覺反饋
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    // 驗證必填字段
    if (!name.trim()) {
      Alert.alert('Validation Error', 'Please enter your name');
      return;
    }

    if (!initials.trim()) {
      Alert.alert('Validation Error', 'Please enter your initials');
      return;
    }

    // 僅在提供電話號碼時驗證格式（香港手機號碼通常為8位數字）
    const formattedPhone = formatPhoneNumber(phoneNumber);
    if (phoneNumber.trim() && (formattedPhone.length !== 8 || !/^\d{8}$/.test(formattedPhone))) {
      Alert.alert('Validation Error', 'Please enter a valid Hong Kong phone number (8 digits)');
      return;
    }

    setLoading(true);

    try {
      const profile: UserProfile = {
        name: name.trim(),
        initials: initials.trim().toUpperCase(),
        role,
        phoneNumber: phoneNumber.trim() ? formattedPhone : undefined,
        color: selectedColor,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      // 先保存到本地存儲
      await StorageService.saveUserProfile(profile);
      
      // 註冊到 Cloud Functions (Firebase)
      try {
        await registerUser({
          nickname: name.trim(),
          name: name.trim(),
          role,
          initials: initials.trim().toUpperCase(),
          color: selectedColor,
          phoneNumber: phoneNumber.trim() ? `+852${formattedPhone}` : undefined, // 加上國家代碼
          avatar: '', // 暫時為空，後續可擴展頭像功能
        });
        
        console.log('用戶已成功註冊到 Firebase');
      } catch (firebaseError) {
        // Firebase 註冊失敗但本地保存成功，記錄錯誤但不阻止用戶操作
        console.error('Firebase 註冊失敗:', firebaseError);
        console.log('本地資料已保存，但 Firebase 同步失敗，將在下次嘗試時重試');
      }
      
      setSnackbarMessage('Profile saved successfully');
      setShowSnackbar(true);
      
      // Delay return to let user see success message
      setTimeout(() => {
        if (router.canGoBack()) {
          router.back();
        } else {
          // If no page to go back to, navigate to home
          router.replace('/');
        }
      }, 1500);
      
    } catch (error) {
      console.error('Failed to save profile:', error);
      Alert.alert('Save Failed', 'An error occurred while saving your profile. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [name, initials, role, phoneNumber, selectedColor]);

  // 處理顏色選擇 - 添加動畫和觸覺反饋
  const handleColorSelect = useCallback((color: string) => {
    // 添加觸覺反饋
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // 顏色按鈕縮放動畫
    const colorAnimation = colorAnimations[color];
    if (colorAnimation) {
      Animated.sequence([
        Animated.timing(colorAnimation, {
          toValue: 0.8,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(colorAnimation, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
    }

    // 頭像顏色變化動畫
    Animated.timing(avatarColorAnimation, {
      toValue: 0.8,
      duration: 150,
      useNativeDriver: true,
    }).start(() => {
      setSelectedColor(color);
      Animated.timing(avatarColorAnimation, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    });
  }, [colorAnimations, avatarColorAnimation]);

  // Handle role selection
  const handleRoleSelect = useCallback((selectedRole: UserRole) => {
    setRole(selectedRole);
    setShowRoleMenu(false);
  }, []);

  if (isInitialLoad) {
    return (
      <View style={[styles.loadingContainer, dynamicStyles.container]}>
        <RNText style={[styles.loadingText, dynamicStyles.textColor]}>
          Loading...
        </RNText>
      </View>
    );
  }

  return (
    <View style={[styles.container, dynamicStyles.container]}>
      {/* 頭部導航 - 使用 X 圖標替代返回箭頭 */}
      <Appbar.Header style={[styles.header, dynamicStyles.header]}>
        <IconButton
          icon="close"
          iconColor={theme.colors.onPrimary}
          size={24}
          onPress={handleGoBack}
          style={styles.closeButton}
        />
        <Appbar.Content
          title="Edit Profile"
          titleStyle={[styles.headerTitle, { color: theme.colors.onPrimary }]}
        />
        {/* 右側佔位符，保持標題居中 */}
        <View style={styles.headerSpacer} />
      </Appbar.Header>

      {/* Main Content */}
      <ScrollView 
        style={[styles.content, dynamicStyles.contentBackground]}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* 頭像預覽 - 添加動畫效果 */}
        <View style={styles.avatarSection}>
          <Animated.View
            style={[
              styles.avatarContainer,
              {
                transform: [{ scale: avatarColorAnimation }],
              },
            ]}
          >
            <Avatar.Text
              size={128}
              label={initials}
              style={[
                styles.avatarPreview,
                { backgroundColor: selectedColor }
              ]}
              labelStyle={[
                styles.avatarText,
                { color: isLightColor(selectedColor) ? '#333333' : '#FFFFFF' }
              ]}
            />
          </Animated.View>
        </View>

        {/* Form Fields */}
        <Card style={[styles.formCard, dynamicStyles.cardBackground]}>
          <Card.Content style={styles.formContent}>
            {/* 姓名輸入框 - 使用圓角設計 */}
            <TextInput
              label="Name"
              value={name}
              onChangeText={setName}
              mode="outlined"
              style={styles.input}
              placeholder="Enter your full name"
              placeholderTextColor={dynamicStyles.placeholderColor}
              outlineStyle={styles.inputOutline}
            />

            {/* 縮寫輸入框 - 使用圓角設計 */}
            <TextInput
              label="Initials"
              value={initials}
              onChangeText={(text) => setInitials(text.toUpperCase())}
              mode="outlined"
              style={styles.input}
              placeholder="e.g., JD"
              placeholderTextColor={dynamicStyles.placeholderColor}
              maxLength={3}
              outlineStyle={styles.inputOutline}
            />

            {/* Role Selection - Enhanced Dropdown */}
            <View style={styles.inputContainer}>
              <RNText style={[styles.label, dynamicStyles.textColor]}>Role</RNText>
              <Menu
                visible={showRoleMenu}
                onDismiss={() => setShowRoleMenu(false)}
                contentStyle={[
                  styles.menuContent,
                  { backgroundColor: theme.colors.surface }
                ]}
                anchor={
                  <TouchableOpacity
                    style={[
                      styles.enhancedMenuButton, 
                      { 
                        borderColor: theme.colors.outline,
                        backgroundColor: dynamicStyles.menuButtonBackground.backgroundColor
                      }
                    ]}
                    onPress={() => setShowRoleMenu(true)}
                    activeOpacity={0.7}
                  >
                    <View style={styles.menuButtonContent}>
                      <RNText style={[styles.menuButtonText, dynamicStyles.textColor]}>
                        {ROLE_OPTIONS.find(option => option.value === role)?.label || 'Select Role'}
                      </RNText>
                      <MaterialIcons 
                        name={showRoleMenu ? "keyboard-arrow-up" : "keyboard-arrow-down"}
                        size={24} 
                        color={theme.colors.onSurface} 
                        style={[
                          styles.dropdownIcon,
                          showRoleMenu && styles.dropdownIconRotated
                        ]}
                      />
                    </View>
                  </TouchableOpacity>
                }
              >
                {ROLE_OPTIONS.map((option) => (
                  <Menu.Item
                    key={option.value}
                    onPress={() => handleRoleSelect(option.value)}
                    title={option.label}
                    titleStyle={[
                      styles.menuItemTitle,
                      { color: theme.colors.onSurface }
                    ]}
                    style={[
                      styles.menuItem,
                      role === option.value && {
                        backgroundColor: theme.colors.primaryContainer
                      }
                    ]}
                  />
                ))}
              </Menu>
            </View>

            {/* Phone Number Input */}
            <View style={styles.inputContainer}>
              <RNText style={[styles.label, dynamicStyles.textColor]}>Phone Number (Hong Kong) - Optional</RNText>
              <View style={styles.phoneInputContainer}>
                <View style={styles.phonePrefix}>
                  <RNText style={[styles.phonePrefixText, { color: theme.colors.onSurfaceVariant }]}>
                    +852
                  </RNText>
                </View>
                <TextInput
                  value={phoneNumber}
                  onChangeText={(text) => setPhoneNumber(formatPhoneNumber(text))}
                  mode="outlined"
                  style={[styles.phoneInput, { paddingLeft: 60 }]}
                  placeholder="Enter your phone number"
                  placeholderTextColor={dynamicStyles.placeholderColor}
                  keyboardType="phone-pad"
                  maxLength={8}
                  outlineStyle={styles.inputOutline}
                />
              </View>
            </View>

            {/* 顏色選擇器 - 添加動畫效果 */}
            <View style={styles.colorSection}>
              <RNText style={[styles.label, dynamicStyles.textColor]}>
                Profile Icon Color
              </RNText>
              <View style={styles.colorGrid}>
                {PROFILE_COLORS.map((color) => (
                  <Animated.View
                    key={color}
                    style={{
                      transform: [{ scale: colorAnimations[color] }],
                    }}
                  >
                    <TouchableOpacity
                      style={[
                        styles.colorOption,
                        { backgroundColor: color },
                        selectedColor === color && styles.selectedColorOption
                      ]}
                      onPress={() => handleColorSelect(color)}
                      activeOpacity={0.8}
                    >
                      {selectedColor === color && (
                        <MaterialIcons
                          name="check"
                          size={20}
                          color={isLightColor(color) ? '#333333' : '#FFFFFF'}
                        />
                      )}
                    </TouchableOpacity>
                  </Animated.View>
                ))}
              </View>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>

      {/* Save Button Footer */}
      <SafeAreaView style={[styles.footer, dynamicStyles.surface]} edges={['bottom']}>
        <Button
          mode="contained"
          onPress={handleSave}
          loading={loading}
          disabled={loading}
          style={styles.saveButton}
          contentStyle={styles.saveButtonContent}
          labelStyle={styles.saveButtonLabel}
        >
          Save Changes
        </Button>
      </SafeAreaView>

      {/* Success Message */}
      <Portal>
        <Snackbar
          visible={showSnackbar}
          onDismiss={() => setShowSnackbar(false)}
          duration={3000}
          action={{
            label: 'OK',
            onPress: () => setShowSnackbar(false),
          }}
        >
          {snackbarMessage}
        </Snackbar>
      </Portal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  closeButton: {
    marginLeft: 4,
  },
  headerSpacer: {
    width: 48, // 與 IconButton 寬度相同，保持標題居中
  },
  content: {
    flex: 1,
    // backgroundColor now handled by dynamicStyles.contentBackground for dark mode support
  },
  contentContainer: {
    padding: 16,
  },
  avatarSection: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  avatarContainer: {
    // 頭像容器，用於動畫效果
  },
  avatarPreview: {
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  avatarText: {
    fontSize: 40,
    fontWeight: 'bold',
  },
  formCard: {
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    borderRadius: 16, // 增加圓角半徑，更符合設計稿
    marginBottom: 24,
  },
  formContent: {
    padding: 24,
  },
  input: {
    marginBottom: 16,
  },
  inputOutline: {
    borderRadius: 12, // 圓角設計，匹配設計稿的 rounded-xl
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 6,
  },
  menuButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 14,
    paddingVertical: 16,
    borderWidth: 1,
    borderRadius: 4,
    backgroundColor: 'transparent',
  },
  menuButtonText: {
    fontSize: 16,
    flex: 1,
  },
  phoneInputContainer: {
    position: 'relative',
  },
  phonePrefix: {
    position: 'absolute',
    left: 14,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    zIndex: 1,
  },
  phonePrefixText: {
    fontSize: 16,
    fontWeight: '500',
  },
  phoneInput: {
    paddingLeft: 60,
  },
  colorSection: {
    marginTop: 8,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16, // 增加間距，更符合設計稿
    marginTop: 12, // 增加頂部間距
    justifyContent: 'flex-start', // 左對齊
  },
  colorOption: {
    width: 44, // 稍微增加大小
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3, // 增加陰影
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  selectedColorOption: {
    elevation: 6, // 選中時更強的陰影
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    transform: [{ scale: 1.15 }], // 稍微增加縮放
  },
  footer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  saveButton: {
    borderRadius: 12, // 圓角設計，匹配設計稿
    elevation: 2, // 添加陰影效果
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  saveButtonContent: {
    paddingVertical: 8,
  },
  saveButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  // Enhanced dropdown menu styles
  menuContent: {
    borderRadius: 12,
    marginTop: 8,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 6,
  },
  enhancedMenuButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderWidth: 1,
    borderRadius: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  menuButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
  },
  dropdownIcon: {
    marginLeft: 8,
  },
  dropdownIconRotated: {
    transform: [{ rotate: '180deg' }],
  },
  menuItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginHorizontal: 4,
    marginVertical: 2,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
}); 