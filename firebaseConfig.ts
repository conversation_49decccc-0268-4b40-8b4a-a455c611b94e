import { initializeApp, getApps, type FirebaseApp } from 'firebase/app';
// const { getAnalytics } = require("firebase/analytics");
import { getAuth, type Auth, connectAuthEmulator } from 'firebase/auth';
import { getDatabase, type Database, connectDatabaseEmulator } from 'firebase/database';
import { getFirestore, type Firestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getFunctions, connectFunctionsEmulator } from 'firebase/functions';

const firebaseConfig = {
  apiKey: "AIzaSyDWmByCJlz3zwo5TE8Vd2Ed4EKmWUlg0Qg",
  authDomain: "qmnoti.firebaseapp.com",
  projectId: "qmnoti",
  storageBucket: "qmnoti.appspot.com",
  messagingSenderId: "590755555605",
  appId: "1:590755555605:web:660cb1745c42231729211c",
  measurementId: "G-8F79PF308H",
  databaseURL: "https://qmnoti-default-rtdb.asia-southeast1.firebasedatabase.app/"
};

// 檢查是否為開發環境（模擬器環境）
const isDevelopment = (typeof __DEV__ !== 'undefined' && __DEV__) || process.env.NODE_ENV === 'development';

// 避免重复初始化Firebase App
let app: FirebaseApp;
if (getApps().length === 0) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApps()[0];
}

// 初始化服务 - 只在需要時初始化
const firestoreDB: Firestore = getFirestore(app);
const realtimeDB: Database = getDatabase(app);
const auth: Auth = getAuth(app);
const functions = getFunctions(app, 'asia-east1');

// 配置模擬器連接（僅在開發環境）
if (isDevelopment) {
  try {
    // 連接到 Firestore 模擬器
    if (!firestoreDB._delegate._databaseId.database.includes('localhost')) {
      connectFirestoreEmulator(firestoreDB, 'localhost', 8080);
      console.log('✅ 已連接到 Firestore 模擬器 (localhost:8080)');
    }
  } catch (error) {
    console.log('ℹ️ Firestore 模擬器已連接或連接失敗:', error);
  }

  try {
    // 連接到 Realtime Database 模擬器
    if (!realtimeDB._delegate._repoInternal.repoInfo_.host.includes('localhost')) {
      connectDatabaseEmulator(realtimeDB, 'localhost', 9000);
      console.log('✅ 已連接到 Database 模擬器 (localhost:9000)');
    }
  } catch (error) {
    console.log('ℹ️ Database 模擬器已連接或連接失敗:', error);
  }

  try {
    // 連接到 Functions 模擬器
    if (!functions._delegate._url?.includes('localhost')) {
      connectFunctionsEmulator(functions, 'localhost', 5001);
      console.log('✅ 已連接到 Functions 模擬器 (localhost:5001)');
    }
  } catch (error) {
    console.log('ℹ️ Functions 模擬器已連接或連接失敗:', error);
  }

  try {
    // 連接到 Auth 模擬器
    if (!auth.config.emulator) {
      connectAuthEmulator(auth, 'http://localhost:9099');
      console.log('✅ 已連接到 Auth 模擬器 (localhost:9099)');
    }
  } catch (error) {
    console.log('ℹ️ Auth 模擬器已連接或連接失敗:', error);
  }

  console.log('🔧 開發環境：已配置所有 Firebase 模擬器連接');
} else {
  console.log('🚀 生產環境：使用 Firebase 生產服務');
}

// 如果你需要 Analytics
// const analytics = getAnalytics(app);

// Android專用：確保React Native Firebase也能工作
console.log('Firebase JS SDK 初始化完成');

export { app, auth, firestoreDB, realtimeDB, functions };
